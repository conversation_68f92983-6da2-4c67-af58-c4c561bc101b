import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import SubmitLevel1RAModal from '../../../src/pages/CreateRA/SubmitLevel1RAModal';

// Mock CustomDatePicker to make testing easier
jest.mock('../../../src/components/CustomDatePicker', () => {
  return function MockCustomDatePicker(props: any) {
    return (
      <div data-testid="custom-date-picker">
        <label>{props.label}</label>
        <input
          data-testid="date-input"
          value={props.value ? props.value.toISOString().split('T')[0] : ''}
          onChange={(e) => {
            const date = e.target.value ? new Date(e.target.value) : undefined;
            props.onChange(date);
          }}
          placeholder={props.placeholder}
          required={props.isRequired}
        />
        {props.errorMsg && <div data-testid="error-message">{props.errorMsg}</div>}
      </div>
    );
  };
});

describe('SubmitLevel1RAModal', () => {
  const defaultProps = {
    show: true,
    onClose: jest.fn(),
    onConfirm: jest.fn(),
    setForm: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders modal when show is true', () => {
    render(<SubmitLevel1RAModal {...defaultProps} />);
    
    expect(screen.getByText('Submitting Level 1 RA')).toBeInTheDocument();
    expect(screen.getByText('Do you want to submit this Level 1 Risk Assessment?')).toBeInTheDocument();
    expect(screen.getByText(/Submitting this would mean auto approval/)).toBeInTheDocument();
  });

  it('does not render modal when show is false', () => {
    render(<SubmitLevel1RAModal {...defaultProps} show={false} />);
    
    expect(screen.queryByText('Submitting Level 1 RA')).not.toBeInTheDocument();
  });

  it('renders all required elements', () => {
    render(<SubmitLevel1RAModal {...defaultProps} />);
    
    // Check modal title
    expect(screen.getByText('Submitting Level 1 RA')).toBeInTheDocument();
    
    // Check warning message
    expect(screen.getByText('Do you want to submit this Level 1 Risk Assessment?')).toBeInTheDocument();
    expect(screen.getByText(/Submitting this would mean auto approval and vessel will be Notified/)).toBeInTheDocument();
    
    // Check date picker
    expect(screen.getByText('Date of Approval')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Select Date')).toBeInTheDocument();
    
    // Check buttons
    expect(screen.getByText('Cancel')).toBeInTheDocument();
    expect(screen.getByText('Confirm')).toBeInTheDocument();
  });

  it('calls onClose when Cancel button is clicked', () => {
    render(<SubmitLevel1RAModal {...defaultProps} />);
    
    fireEvent.click(screen.getByText('Cancel'));
    expect(defaultProps.onClose).toHaveBeenCalledTimes(1);
  });

  it('calls onClose when close button is clicked', () => {
    render(<SubmitLevel1RAModal {...defaultProps} />);
    
    const closeButton = screen.getByRole('button', { name: /close/i });
    fireEvent.click(closeButton);
    expect(defaultProps.onClose).toHaveBeenCalledTimes(1);
  });

  it('Confirm button is disabled when no approval date is selected', () => {
    render(<SubmitLevel1RAModal {...defaultProps} />);
    
    const confirmButton = screen.getByText('Confirm');
    expect(confirmButton).toBeDisabled();
  });

  it('calls onConfirm when Confirm button is clicked with approval date', async () => {
    render(<SubmitLevel1RAModal {...defaultProps} />);

    // Set a date first to enable the button
    const dateInput = screen.getByTestId('date-input');
    fireEvent.change(dateInput, { target: { value: '2024-01-01' } });

    // Wait for the button to be enabled
    await waitFor(() => {
      const confirmButton = screen.getByText('Confirm');
      expect(confirmButton).not.toBeDisabled();
    });

    // Click the confirm button
    const confirmButton = screen.getByText('Confirm');
    fireEvent.click(confirmButton);

    // Check that onConfirm was called (this tests the handleSubmit function)
    expect(defaultProps.onConfirm).toHaveBeenCalledTimes(1);
  });

  it('updates form when date is changed', () => {
    render(<SubmitLevel1RAModal {...defaultProps} />);

    // Test the onChange handler of CustomDatePicker
    const dateInput = screen.getByTestId('date-input');
    fireEvent.change(dateInput, { target: { value: '2024-01-01' } });

    // Check that setForm was called with the updated approval_date
    expect(defaultProps.setForm).toHaveBeenCalled();
  });

  it('enables Confirm button when approval date is selected', async () => {
    render(<SubmitLevel1RAModal {...defaultProps} />);

    const confirmButton = screen.getByText('Confirm');
    expect(confirmButton).toBeDisabled();

    // Set a date
    const dateInput = screen.getByTestId('date-input');
    fireEvent.change(dateInput, { target: { value: '2024-01-01' } });

    // Button should now be enabled
    await waitFor(() => {
      expect(confirmButton).not.toBeDisabled();
    });
  });

  it('calls setForm with previous state and approval_date when date changes', () => {
    render(<SubmitLevel1RAModal {...defaultProps} />);

    const dateInput = screen.getByTestId('date-input');
    fireEvent.change(dateInput, { target: { value: '2024-01-01' } });

    // Check that setForm was called with a function that updates the previous state
    expect(defaultProps.setForm).toHaveBeenCalledWith(expect.any(Function));

    // Test the function that was passed to setForm
    const setFormCall = defaultProps.setForm.mock.calls[0][0];
    const previousState = { someField: 'value' };
    const result = setFormCall(previousState);

    expect(result).toEqual({
      ...previousState,
      approval_date: undefined, // This will be undefined because the date state hasn't been updated yet
    });
  });

  it('clears approval date when empty value is set', async () => {
    render(<SubmitLevel1RAModal {...defaultProps} />);

    const dateInput = screen.getByTestId('date-input');

    // First set a date
    fireEvent.change(dateInput, { target: { value: '2024-01-01' } });

    await waitFor(() => {
      const confirmButton = screen.getByText('Confirm');
      expect(confirmButton).not.toBeDisabled();
    });

    // Then clear it
    fireEvent.change(dateInput, { target: { value: '' } });

    await waitFor(() => {
      const confirmButton = screen.getByText('Confirm');
      expect(confirmButton).toBeDisabled();
    });
  });

  it('renders warning message with correct styling', () => {
    render(<SubmitLevel1RAModal {...defaultProps} />);

    // Check for the warning message content instead of specific styling
    expect(screen.getByText('Do you want to submit this Level 1 Risk Assessment?')).toBeInTheDocument();
    expect(screen.getByText(/Submitting this would mean auto approval and vessel will be Notified/)).toBeInTheDocument();
  });

  it('renders CustomDatePicker with correct props', () => {
    render(<SubmitLevel1RAModal {...defaultProps} />);
    
    expect(screen.getByText('Date of Approval')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Select Date')).toBeInTheDocument();
  });

  it('has correct modal structure', () => {
    render(<SubmitLevel1RAModal {...defaultProps} />);
    
    // Check modal header
    expect(screen.getByText('Submitting Level 1 RA')).toBeInTheDocument();
    
    // Check modal body content
    expect(screen.getByText('Do you want to submit this Level 1 Risk Assessment?')).toBeInTheDocument();
    
    // Check modal footer buttons
    expect(screen.getByText('Cancel')).toBeInTheDocument();
    expect(screen.getByText('Confirm')).toBeInTheDocument();
  });
});
